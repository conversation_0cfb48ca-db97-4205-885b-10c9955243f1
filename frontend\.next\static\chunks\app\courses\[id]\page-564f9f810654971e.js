(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[415],{3307:(e,t,s)=>{Promise.resolve().then(s.bind(s,9602))},5695:(e,t,s)=>{"use strict";var a=s(8999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}})},9602:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(5155),r=s(5494),n=s(2115),l=s(4267);let o=e=>{let{isOpen:t,onClose:s,courseId:r}=e,o={apiKey:"NDM2Y2M0NDI0YTMzNGM1OWFkZThhZTRmMjJhZmEzNzYtMTc0NzA1OTcyOA==",serverUrl:"https://api.heygen.com"},[i,c]=(0,n.useState)(null),[d,u]=(0,n.useState)(null),[m,x]=(0,n.useState)(null),[h,g]=(0,n.useState)(null),[p,f]=(0,n.useState)(null),[v,b]=(0,n.useState)("Pedro_Chair_Sitting_public"),[j,y]=(0,n.useState)(""),[w,N]=(0,n.useState)(""),[k,S]=(0,n.useState)([]),[C,_]=(0,n.useState)(!1),[A,z]=(0,n.useState)(!1),[R,T]=(0,n.useState)(!1),[D,M]=(0,n.useState)(null),[P,E]=(0,n.useState)(!1),O=(0,n.useRef)(null),L=(0,n.useRef)(null),B=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",s={message:e,type:t,timestamp:new Date().toLocaleTimeString()};S(e=>[...e,s])};(0,n.useEffect)(()=>{L.current&&(L.current.scrollTop=L.current.scrollHeight)},[k]),(0,n.useEffect)(()=>{if(t){S([{message:"Welcome to AI Avatar Assistant!",type:"system",timestamp:new Date().toLocaleTimeString()},{message:"Auto-starting avatar session...",type:"info",timestamp:new Date().toLocaleTimeString()},{message:"Please wait while we initialize your AI assistant",type:"info",timestamp:new Date().toLocaleTimeString()}]),z(!0);let e=setTimeout(async()=>{try{await Y(),z(!1)}catch(e){console.error("Auto-start failed:",e),z(!1)}},1e3);return()=>{clearTimeout(e),z(!1)}}z(!1)},[t]),(0,n.useEffect)(()=>{{let e=window.SpeechRecognition||window.webkitSpeechRecognition;if(e){E(!0);let t=new e;t.continuous=!1,t.interimResults=!1,t.lang="en-US",t.onresult=e=>{let t=e.results[0][0].transcript;N(t),B('\uD83C\uDFA4 Voice input: "'.concat(t,'"'),"success")},t.onerror=e=>{B("Voice recognition error: ".concat(e.error),"error"),T(!1)},t.onend=()=>{T(!1)},M(t)}else E(!1),B("Voice input not supported in this browser","warning")}},[]);let I=async()=>{try{let e=await fetch("".concat(o.serverUrl,"/v1/streaming.create_token"),{method:"POST",headers:{"Content-Type":"application/json","X-Api-Key":o.apiKey}});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=await e.json();if(!t.data||!t.data.token)throw Error("Invalid token data received from server");return u(t.data.token),B("Session token obtained successfully","success"),t.data.token}catch(e){throw B("Failed to get session token: ".concat(e.message),"error"),e}},V=async(e,t)=>{try{if(!t)throw Error("No session token available for WebSocket connection");let s=new URLSearchParams({session_id:e,session_token:t,silence_response:"false",opening_text:"Hello, how can I help you?",stt_language:"en"}),a="wss://".concat(new URL(o.serverUrl).hostname,"/v1/ws/streaming.chat?").concat(s),r=new WebSocket(a);f(r),r.addEventListener("message",e=>{let t=JSON.parse(e.data);console.log("Raw WebSocket event:",t)}),r.addEventListener("error",e=>{B("WebSocket connection error","error"),console.error("WebSocket error:",e)}),r.addEventListener("open",()=>{B("WebSocket connected successfully","success")})}catch(e){throw B("WebSocket connection failed: ".concat(e.message),"error"),e}},q=async()=>{try{let e=await I();B("Debug: Creating session with token: ".concat(e?"Token exists":"No token"),"info");let t=await fetch("".concat(o.serverUrl,"/v1/streaming.new"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({quality:"high",avatar_name:v,voice:{voice_id:j,rate:1},version:"v2",video_encoding:"H264"})});if(B("Debug: Create session response status: ".concat(t.status),"info"),!t.ok){let e=await t.text();throw B("Debug: Create session error: ".concat(e),"error"),Error("HTTP error! status: ".concat(t.status," - ").concat(e))}let s=await t.json();if(!s.data||!s.data.session_id)throw Error("Invalid session data received from server");B("Debug: Session data keys: ".concat(Object.keys(s.data).join(", ")),"info"),c(s.data);let a=new l.Wv({adaptiveStream:!0,dynacast:!0,videoCaptureDefaults:{resolution:l.gF.h720.resolution}});x(a),a.on(l.u9.DataReceived,e=>{let t=new TextDecoder().decode(e);console.log("Room message:",JSON.parse(t))});let r=new MediaStream;return g(r),a.on(l.u9.TrackSubscribed,e=>{("video"===e.kind||"audio"===e.kind)&&(r.addTrack(e.mediaStreamTrack),r.getVideoTracks().length>0&&r.getAudioTracks().length>0&&O.current&&(O.current.srcObject=r,B("Media stream ready - Avatar is live!","success")))}),a.on(l.u9.TrackUnsubscribed,e=>{let t=e.mediaStreamTrack;t&&r.removeTrack(t)}),a.on(l.u9.Disconnected,e=>{B("Room disconnected: ".concat(e),"warning")}),await a.prepareConnection(s.data.url,s.data.access_token),B("Connection prepared successfully","success"),await V(s.data.session_id,e),B("Session created successfully","success"),{sessionData:s.data,token:e,room:a}}catch(e){throw B("Failed to create session: ".concat(e.message),"error"),e}},H=async(e,t,s)=>{if(!e||!e.session_id)throw Error("No session info available");try{B("Debug: Using token: ".concat(t?"Token exists":"No token"),"info"),B("Debug: Session ID: ".concat(e.session_id),"info");let a=await fetch("".concat(o.serverUrl,"/v1/streaming.start"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({session_id:e.session_id})});if(B("Debug: Start response status: ".concat(a.status),"info"),!a.ok){let e=await a.text();throw B("Debug: Error response: ".concat(e),"error"),Error("HTTP error! status: ".concat(a.status," - ").concat(e))}B("Debug: Available session properties: ".concat(Object.keys(e).join(", ")),"info"),B("Debug: Room exists: ".concat(!!s),"info"),B("Debug: URL exists: ".concat(!!e.url),"info"),B("Debug: Access token exists: ".concat(!!e.access_token),"info"),s&&e.url&&e.access_token?(await s.connect(e.url,e.access_token),B("Connected to room successfully","success")):(B("Warning: Room or connection details missing","warning"),B("Debug: Missing - Room: ".concat(!s,", URL: ").concat(!e.url,", Token: ").concat(!e.access_token),"error")),_(!0),B("Streaming started successfully","success")}catch(e){throw B("Failed to start streaming: ".concat(e.message),"error"),e}},U=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"talk";if(!i)return void B("No active session - please start the avatar first","warning");if(!d)return void B("No session token available","error");try{B("Sending message to avatar...","info");let s=await fetch("".concat(o.serverUrl,"/v1/streaming.task"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(d)},body:JSON.stringify({session_id:i.session_id,text:e,task_type:t})}),a=await s.json();!s.ok||a.error?B("HeyGen API error: ".concat(a.error||s.statusText),"error"):(B("✓ Message sent successfully (".concat(t,")"),"success"),B('Avatar will speak: "'.concat(e.substring(0,50)).concat(e.length>50?"...":"",'"'),"info"))}catch(e){B("HeyGen API call failed: "+e.message,"error")}},W=async e=>{try{var t,s,a;let r=await fetch("http://*************:5001/api/openai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:e})}),n=await r.json();if(!r.ok||!n.choices){let e=n.error?"object"==typeof n.error?JSON.stringify(n.error):n.error:r.statusText;return B("Azure OpenAI error: "+e,"error"),null}return null==(a=n.choices)||null==(s=a[0])||null==(t=s.message)?void 0:t.content}catch(e){return B("Azure OpenAI call failed: "+e.message,"error"),null}},F=async e=>{try{let t=await fetch("http://*************:5001/api/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,courseId:r})}),s=await t.json();if(!t.ok||!s.value){let e=s.error?"object"==typeof s.error?JSON.stringify(s.error):s.error:t.statusText;return B("Azure Search error: "+e,"error"),null}return B("Found ".concat(s.value.length," relevant documents"),"success"),(s.value||[]).map(e=>e.content).join("\n\n")}catch(e){return B("Azure Search call failed: "+e.message,"error"),null}},J=async()=>{let e=w.trim();if(!e)return void B("Please enter a question first","warning");if(!i)return void B("Please start the avatar session first","warning");try{B('\uD83D\uDD0D Searching for: "'.concat(e,'"'),"info");let t=await F(e);if(!t)return void B("No relevant documents found for your question","warning");B("\uD83E\uDD16 Processing with AI...","info");let s=await W('Based on the following context, answer the question: "'.concat(e,'"\n\nContext: ').concat(t));if(s){B("\uD83D\uDCA1 AI Response: ".concat(s.substring(0,100)).concat(s.length>100?"...":""),"success");let e=s.replace(/[#*_`>-]+/g,"").replace(/\n{2,}/g," ").replace(/\s{2,}/g," ").trim();B("\uD83C\uDFA4 Avatar speaking response...","info"),await U(e,"repeat"),N("")}else B("Failed to generate AI response","error")}catch(e){B("Error processing question: ".concat(e.message),"error")}},Y=async()=>{try{let e=await q();await H(e.sessionData,e.token,e.room)}catch(e){B("Error starting session: ".concat(e.message),"error")}};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold",children:"AI Avatar Assistant"}),(0,a.jsx)("p",{className:"text-white text-opacity-70 text-sm",children:"Powered by EXL"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(C?"bg-green-400 animate-pulse":"bg-gray-400")}),(0,a.jsx)("span",{className:"text-white text-opacity-80 text-sm font-medium",children:C?"Live":"Offline"})]}),(0,a.jsx)("button",{onClick:s,className:"text-white hover:text-gray-200 transition-colors",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]})}),(0,a.jsx)("div",{className:"p-6 max-h-[calc(90vh-120px)] overflow-y-auto",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-5 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-6 border border-gray-200",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 mb-4 flex items-center",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-2 text-cyan-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",clipRule:"evenodd"})}),"Ask Your Question"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)("textarea",{placeholder:"Type your question here...",value:w,onChange:e=>N(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),J())},className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200 resize-none h-24"})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("button",{onClick:J,className:"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg",children:[(0,a.jsx)("svg",{className:"w-4 h-4 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Ask AI"]}),(0,a.jsxs)("button",{onClick:()=>{if(!P)return void B("Voice input not supported in this browser","warning");if(!D)return void B("Voice recognition not initialized","error");if(R)D.stop(),T(!1),B("Voice recording stopped","info");else try{D.start(),T(!0),B("\uD83C\uDFA4 Listening... Speak your question","info")}catch(e){B("Failed to start voice recording: ".concat(e.message),"error"),T(!1)}},disabled:!P,className:"px-4 py-3 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg ".concat(R?"bg-red-500 hover:bg-red-600 animate-pulse":P?"bg-purple-500 hover:bg-purple-600":"bg-gray-400 cursor-not-allowed"),children:[(0,a.jsx)("svg",{className:"w-4 h-4 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:R?(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a2 2 0 114 0v4a2 2 0 11-4 0V7z",clipRule:"evenodd"}):(0,a.jsx)("path",{fillRule:"evenodd",d:"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z",clipRule:"evenodd"})}),R?"Stop Recording":"Voice Input"]})]})]})]})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-6 border border-gray-200 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold text-gray-800 flex items-center",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-2 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"})}),"AI Avatar"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(A?"bg-yellow-500 animate-pulse":C?"bg-green-500 animate-pulse":"bg-gray-400")}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:A?"Starting...":C?"Connected":"Ready"})]})]}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("video",{ref:O,className:"w-full h-96 bg-gray-900 rounded-xl object-cover shadow-lg",autoPlay:!0,children:(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-white",children:[(0,a.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4 opacity-50",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z",clipRule:"evenodd"})}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"Avatar will appear here"}),(0,a.jsx)("p",{className:"text-sm opacity-75",children:"Auto-starting avatar session..."})]})})})})]})})]})})]})}):null};function i(e){let{isOpen:t,onClose:s,courseId:r}=e,[l,o]=(0,n.useState)(null),[i,c]=(0,n.useState)(!1),[d,u]=(0,n.useState)(0),[m,x]=(0,n.useState)({}),[h,g]=(0,n.useState)(!1),[p,f]=(0,n.useState)(null),[v,b]=(0,n.useState)(!1);(0,n.useEffect)(()=>{t&&!l&&j()},[t]);let j=async()=>{try{c(!0);let e=localStorage.getItem("token"),t=await fetch("http://*************:5001/api/assessments/approved",{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(t.ok){let s=(await t.json()).find(e=>e.course_id===parseInt(r));if(s){let t=await fetch("".concat("http://*************:5001","/api/assessments/approved/").concat(s.id),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(t.ok){let e=await t.json();o(e)}}}}catch(e){console.error("Error fetching assessment:",e)}finally{c(!1)}},y=(e,t)=>{x(s=>({...s,[e]:t}))},w=()=>{x({}),u(0),g(!1),f(null),b(!1)},N=()=>{w(),s()};if(!t)return null;let k=null==l?void 0:l.questions[d],S=Object.keys(m).length,C=(null==l?void 0:l.total_questions)||0;return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"Course Assessment"}),(0,a.jsx)("button",{onClick:N,className:"text-gray-400 hover:text-gray-600 text-2xl",children:"\xd7"})]}),(0,a.jsx)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:i?(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("div",{className:"text-lg text-gray-600",children:"Loading assessment..."})}):l?v?h?(0,a.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[(0,a.jsx)("div",{className:"w-24 h-24 rounded-full mx-auto mb-6 flex items-center justify-center ".concat(p>=70?"bg-green-100":"bg-red-100"),children:(0,a.jsxs)("span",{className:"text-3xl font-bold ".concat(p>=70?"text-green-600":"text-red-600"),children:[p,"%"]})}),(0,a.jsx)("h3",{className:"text-2xl font-semibold mb-4 ".concat(p>=70?"text-green-800":"text-red-800"),children:p>=70?"Congratulations!":"Keep Learning!"}),(0,a.jsxs)("p",{className:"text-gray-600 mb-8",children:["You scored ",p,"% on this assessment.",p>=70?" You passed!":" You need 70% to pass."]}),(0,a.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,a.jsx)("button",{onClick:w,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:"Retake Assessment"}),(0,a.jsx)("button",{onClick:N,className:"bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:"Close"})]})]}):k&&(0,a.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:["Question ",d+1," of ",C]}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[S,"/",C," answered"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-orange-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat((d+1)/C*100,"%")}})})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-6",children:k.question_text}),(0,a.jsx)("div",{className:"space-y-3",children:[{letter:"A",text:k.option_a},{letter:"B",text:k.option_b},{letter:"C",text:k.option_c},{letter:"D",text:k.option_d}].map(e=>(0,a.jsxs)("label",{className:"flex items-center gap-4 p-4 rounded-lg border cursor-pointer transition-all ".concat(m[k.id]===e.letter?"bg-orange-50 border-orange-200":"bg-gray-50 border-gray-200 hover:bg-gray-100"),children:[(0,a.jsx)("input",{type:"radio",name:"question-".concat(k.id),value:e.letter,checked:m[k.id]===e.letter,onChange:()=>y(k.id,e.letter),className:"text-orange-500"}),(0,a.jsx)("span",{className:"w-8 h-8 rounded-full bg-white border-2 border-gray-300 flex items-center justify-center text-sm font-medium",children:e.letter}),(0,a.jsx)("span",{className:"text-gray-700 flex-1",children:e.text})]},e.letter))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("button",{onClick:()=>{d>0&&u(d-1)},disabled:0===d,className:"bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg font-medium transition-colors",children:"Previous"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Please answer all questions before submitting"}),d===C-1?(0,a.jsx)("button",{onClick:()=>{if(!l)return;let e=0;l.questions.forEach(t=>{m[t.id]===t.correct_answer&&e++}),f(Math.round(e/l.questions.length*100)),g(!0)},disabled:S!==C,className:"bg-green-500 hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg font-medium transition-colors",children:"Submit Assessment"}):(0,a.jsx)("button",{onClick:()=>{d<l.questions.length-1&&u(d+1)},className:"bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:"Next"})]})]}):(0,a.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-orange-600",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsxs)("h3",{className:"text-xl font-semibold text-gray-800 mb-2",children:[l.course_title," - Assessment"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:"Assessment Details"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600",children:[(0,a.jsxs)("li",{children:["• ",l.total_questions," multiple choice questions"]}),(0,a.jsx)("li",{children:"• 60 minutes time limit"}),(0,a.jsx)("li",{children:"• 70% required to pass"}),(0,a.jsx)("li",{children:"• 3 attempts allowed"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:"Your Progress"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600",children:[(0,a.jsx)("li",{children:"• Attempts used: 0/3"}),(0,a.jsx)("li",{children:"• Best score: 0%"}),(0,a.jsx)("li",{children:"• Status: Not passed"})]})]})]}),(0,a.jsx)("button",{onClick:()=>b(!0),className:"w-full bg-orange-500 hover:bg-orange-600 text-white py-3 rounded-lg font-medium transition-colors",children:"Start Assessment"})]}):(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-yellow-800 mb-2",children:"No Assessment Available"}),(0,a.jsx)("p",{className:"text-yellow-700",children:"No assessment has been created for this course yet."})]})})})]})})}var c=s(5695);function d(){let e=(0,c.useParams)(),t=(0,c.useRouter)(),s=e.id,[l,d]=(0,n.useState)("Overview"),[u,m]=(0,n.useState)(!1),[x,h]=(0,n.useState)(null),[g,p]=(0,n.useState)([]),[f,v]=(0,n.useState)(0),[b,j]=(0,n.useState)(!0),[y,w]=(0,n.useState)(null),[N,k]=(0,n.useState)(!0),[S,C]=(0,n.useState)(!0),[_,A]=(0,n.useState)(!1),[z,R]=(0,n.useState)(null),[T,D]=(0,n.useState)([]),[M,P]=(0,n.useState)(null),[E,O]=(0,n.useState)(!1),L=()=>{let e=localStorage.getItem("token");if(e)try{let t=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),s=decodeURIComponent(atob(t).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(s)}catch(e){console.error("Error decoding token:",e)}return null};(0,n.useEffect)(()=>{let e=L();e?P(e):window.location.href="/signin"},[]);let B=null==M?void 0:M.id,I=(0,n.useCallback)(async()=>{if(B)try{let e=await fetch("".concat("http://*************:5001","/api/users/").concat(B,"/courses/").concat(s,"/progress"));if(e.ok){let t=await e.json();R(t.courseProgress),D(t.moduleProgress),t.courseProgress&&t.courseProgress.current_module>1&&v(t.courseProgress.current_module-1)}}catch(e){console.error("Error fetching progress:",e)}},[B,s]),V=(0,n.useCallback)(async()=>{if(B)try{(await fetch("".concat("http://*************:5001","/api/courses/").concat(s,"/start"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:B})})).ok&&(A(!0),await I(),console.log("Course started for course ID:",s))}catch(e){console.error("Error starting course:",e)}},[B,s,I]),q=(0,n.useCallback)(async function(e,t,a){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(B)try{let n=g[e-1],l={userId:B,watchTime:t,totalDuration:a,isCompleted:r,moduleTitle:(null==n?void 0:n.page_title)||"Module ".concat(e)};console.log("Updating module progress:",l);let o=await fetch("".concat("http://*************:5001","/api/courses/").concat(s,"/modules/").concat(e,"/progress"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(o.ok){let e=await o.json();console.log("Progress update successful:",e),await I()}else{let e=await o.json();console.error("Progress update failed:",e)}}catch(e){console.error("Error updating module progress:",e)}},[B,s,g,I]);if((0,n.useEffect)(()=>{let e=async()=>{try{j(!0);let e=await fetch("".concat("http://*************:5001","/api/courses/").concat(s));if(!e.ok)throw Error("Failed to fetch course details");let t=await e.json();h(t);let a=await fetch("".concat("http://*************:5001","/api/courses/").concat(s,"/videos"));if(!a.ok)throw Error("Failed to fetch course videos");let r=await a.json();p(r),B&&await I()}catch(e){w(e instanceof Error?e.message:"An error occurred")}finally{j(!1)}};s&&B&&e()},[s,B]),(0,n.useEffect)(()=>{let e=e=>{0!==g.length&&("ArrowLeft"===e.key&&f>0?v(f-1):"ArrowRight"===e.key&&f<g.length-1&&v(f+1))};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[f,g.length]),b)return(0,a.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,a.jsx)(r.default,{}),(0,a.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-lg text-gray-600",children:"Loading course..."})})})]});if(y||!x)return(0,a.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,a.jsx)(r.default,{}),(0,a.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-lg text-red-600",children:y||"Course not found"})})})]});let H=g[f];return(0,a.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-50",children:[(0,a.jsx)(r.default,{}),(0,a.jsx)("main",{className:"flex-grow",children:(0,a.jsxs)("div",{className:"flex h-[calc(100vh-64px)] relative",children:[(0,a.jsxs)("div",{className:"".concat(N?"w-80":"w-0"," bg-white border-r border-gray-200 flex flex-col transition-all duration-300 overflow-hidden"),children:[(0,a.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("svg",{className:"w-5 h-5 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z",clipRule:"evenodd"})}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-800",children:"Course Curriculum"})]}),g.length>0&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[g.length," modules"]})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:g.length>0?(0,a.jsxs)("div",{className:"p-2",children:[g.map((e,t)=>{let s=T.find(t=>t.module_number===e.page_number),r=(null==s?void 0:s.is_completed)||!1,n=(null==s?void 0:s.completion_percentage)||0;return(0,a.jsx)("div",{className:"p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 ".concat(t===f?"bg-blue-50 border-l-4 border-blue-500":"hover:bg-gray-50"," ").concat(r?"bg-green-50":""),onClick:()=>{v(t),_||V()},children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ".concat(r?"bg-green-500 text-white":t===f?"bg-blue-500 text-white":"bg-gray-100 text-gray-600"),children:r?(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}):t===f?(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z",clipRule:"evenodd"})}):e.page_number}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("h3",{className:"font-medium text-sm leading-tight ".concat(r?"text-green-800":t===f?"text-blue-800":"text-gray-800"),children:["Module ",e.page_number,": ",e.page_title]}),(0,a.jsxs)("p",{className:"text-xs mt-1 ".concat(r?"text-green-600":t===f?"text-blue-600":"text-gray-500"),children:[e.avatar_name," • 30 min"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,a.jsx)("p",{className:"text-xs ".concat(r?"text-green-600":t===f?"text-blue-600":"text-gray-500"),children:r?"Completed":n>0?"".concat(Math.round(n),"% watched"):"Not started"}),n>0&&!r&&(0,a.jsx)("div",{className:"flex-1 bg-gray-200 rounded-full h-1",children:(0,a.jsx)("div",{className:"bg-blue-500 h-1 rounded-full transition-all duration-300",style:{width:"".concat(n,"%")}})})]})]})]})},e.id)}),(0,a.jsx)("div",{className:"mt-4 p-3 rounded-lg border cursor-pointer transition-all ".concat((null==z?void 0:z.progress_percentage)===100?"bg-green-50 border-green-200 hover:bg-green-100":"bg-gray-50 border-gray-200 cursor-not-allowed opacity-60"),onClick:()=>{(null==z?void 0:z.progress_percentage)===100&&m(!0)},children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat((null==z?void 0:z.progress_percentage)===100?"bg-green-100":"bg-gray-100"),children:(0,a.jsx)("svg",{className:"w-4 h-4 ".concat((null==z?void 0:z.progress_percentage)===100?"text-green-600":"text-gray-400"),fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-sm ".concat((null==z?void 0:z.progress_percentage)===100?"text-green-800":"text-gray-500"),children:"Take Final Assessment"}),(0,a.jsx)("p",{className:"text-xs ".concat((null==z?void 0:z.progress_percentage)===100?"text-green-600":"text-gray-400"),children:(null==z?void 0:z.progress_percentage)===100?"Click to start assessment":"Complete all modules to unlock assessment"})]})]})})]}):(0,a.jsx)("div",{className:"p-4 text-center text-gray-500",children:(0,a.jsx)("p",{children:"No curriculum available"})})})]}),(0,a.jsx)("button",{onClick:()=>k(!N),className:"absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-yellow-400 hover:bg-yellow-500 text-black p-2 rounded-r-lg shadow-lg transition-all duration-200",style:{left:N?"320px":"0px"},children:(0,a.jsx)("svg",{className:"w-5 h-5 transition-transform duration-200 ".concat(N?"rotate-180":""),fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsxs)("div",{className:"bg-black relative",children:[g.length>0&&H&&H.heygenbloburl?(0,a.jsxs)("video",{className:"w-full h-[400px] object-cover",controls:!0,autoPlay:!0,onPlay:()=>{_||V()},onTimeUpdate:e=>{let t=e.target,s=Math.floor(t.currentTime),a=Math.floor(t.duration);s%10==0&&s>0&&q(H.page_number,s,a)},onEnded:()=>{let e=document.querySelector("video");if(e){let t=Math.floor(e.duration);q(H.page_number,t,t,!0)}setTimeout(()=>{f<g.length-1&&v(f+1)},1500)},children:[(0,a.jsx)("source",{src:H.heygenbloburl,type:"video/mp4"}),"Your browser does not support the video tag."]},H.heygenbloburl):(0,a.jsx)("div",{className:"w-full h-[400px] flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-lg",children:0===g.length?"No videos available for this course":"Loading video..."})}),(0,a.jsx)("button",{onClick:()=>O(!0),className:"absolute top-4 right-4 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg transition-all duration-200 transform hover:scale-110 group z-10",title:"Raise Hand",children:(0,a.jsx)("svg",{className:"w-6 h-6 group-hover:animate-bounce",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M9 3a1 1 0 012 0v5.5a.5.5 0 001 0V4a1 1 0 112 0v4.5a.5.5 0 001 0V6a1 1 0 112 0v6a7 7 0 11-14 0V9a1 1 0 012 0v2.5a.5.5 0 001 0V4a1 1 0 012 0v4.5a.5.5 0 001 0V3z",clipRule:"evenodd"})})})]}),(0,a.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,a.jsx)("div",{className:"flex space-x-8 px-6",children:["Overview","Assessment","Discussion","Reviews"].map(e=>(0,a.jsx)("button",{className:"py-4 font-medium border-b-2 transition-colors ".concat(l===e&&"Assessment"!==e&&"Discussion"!==e?"border-orange-500 text-orange-600":"border-transparent text-gray-500 hover:text-gray-700"),onClick:()=>{"Assessment"===e?m(!0):"Discussion"===e?t.push("/courses/".concat(s,"/discussion")):d(e)},children:e},e))})}),(0,a.jsxs)("div",{className:"flex-1 bg-white p-6 overflow-y-auto",children:["Overview"===l&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"About This Course"}),(0,a.jsx)("p",{className:"text-gray-700 mb-6",children:"Comprehensive training in cardiovascular medicine covering diagnosis, treatment, and patient care protocols."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"What you'll learn"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-700",children:[(0,a.jsxs)("li",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{className:"text-green-500 mt-1",children:"•"}),"Industry best practices and standards"]}),(0,a.jsxs)("li",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{className:"text-green-500 mt-1",children:"•"}),"Practical implementation strategies"]}),(0,a.jsxs)("li",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{className:"text-green-500 mt-1",children:"•"}),"Real-world case studies and examples"]}),(0,a.jsxs)("li",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{className:"text-green-500 mt-1",children:"•"}),"Professional certification preparation"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Prerequisites"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4",children:"Advanced knowledge and experience required"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Course Details"}),(0,a.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,a.jsxs)("li",{children:["Domain: ",x.domain]}),(0,a.jsxs)("li",{children:["Level: ",x.level]}),(0,a.jsxs)("li",{children:["Duration: ",x.estimated_hours," hours"]}),(0,a.jsxs)("li",{children:["Modules: ",g.length]}),(0,a.jsx)("li",{children:"Certificate: Available upon completion"})]})]})]})]}),"Reviews"===l&&(0,a.jsx)("div",{className:"text-gray-500 text-center py-8",children:"Student reviews coming soon..."})]})]}),(0,a.jsx)("button",{onClick:()=>C(!S),className:"absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-yellow-400 hover:bg-yellow-500 text-black p-2 rounded-l-lg shadow-lg transition-all duration-200",style:{right:S?"320px":"0px"},children:(0,a.jsx)("svg",{className:"w-5 h-5 transition-transform duration-200 ".concat(S?"":"rotate-180"),fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"".concat(S?"w-80":"w-0"," bg-white border-l border-gray-200 flex flex-col transition-all duration-300 overflow-hidden"),children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 mb-2",children:"Course Info"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-3",children:x.title}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("span",{className:"inline-block bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full font-medium",children:x.domain})}),(0,a.jsx)("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("svg",{className:"w-4 h-4 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),(0,a.jsx)("span",{children:"(234 reviews)"})]})}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-gray-600 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"})}),(0,a.jsxs)("span",{children:[x.estimated_hours," hours"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"})}),(0,a.jsxs)("span",{children:[g.length," students"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z",clipRule:"evenodd"})}),(0,a.jsx)("span",{children:x.level})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),(0,a.jsx)("span",{children:"Certificate"})]})]})]}),g.length>0&&z&&(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-3",children:"Course Progress"}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Overall Progress"}),(0,a.jsxs)("span",{className:"text-sm font-semibold text-orange-600",children:[Math.round(z.progress_percentage),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-3",children:(0,a.jsx)("div",{className:"bg-orange-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(z.progress_percentage,"%")}})}),(0,a.jsxs)("div",{className:"space-y-1 text-xs text-gray-600",children:[(0,a.jsxs)("p",{children:[z.completed_modules," of ",z.total_modules," modules completed"]}),(0,a.jsxs)("p",{children:["Status: ",(0,a.jsx)("span",{className:"font-medium ".concat("completed"===z.status?"text-green-600":"in_progress"===z.status?"text-blue-600":"text-gray-600"),children:"completed"===z.status?"Completed":"in_progress"===z.status?"In Progress":"Not Started"})]}),z.started_at&&(0,a.jsxs)("p",{children:["Started: ",new Date(z.started_at).toLocaleDateString()]}),z.completed_at&&(0,a.jsxs)("p",{children:["Completed: ",new Date(z.completed_at).toLocaleDateString()]})]})]}),H&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-blue-800 mb-1",children:"Currently Watching"}),(0,a.jsxs)("p",{className:"text-sm text-blue-700",children:["Module ",H.page_number,": ",H.page_title]}),(0,a.jsx)("p",{className:"text-xs text-blue-600 mt-1",children:H.avatar_name})]})]})]})]})}),(0,a.jsx)(o,{isOpen:E,onClose:()=>O(!1),courseId:s}),(0,a.jsx)(i,{isOpen:u,onClose:()=>m(!1),courseId:s})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[266,501,298,581,494,441,684,358],()=>t(3307)),_N_E=e.O()}]);